# 市场
MARKET_SZ = 0  # 深市
MARKET_SH = 1  # 沪市
MARKET_BJ = 2  # 北交

# K线种类
# 0 -   5 分钟K 线
KLINE_5MIN = 0
# 1 -   15 分钟K 线
KLINE_15MIN = 1
# 2 -   30 分钟K 线
KLINE_30MIN = 2
# 3 -   1 小时K 线
KLINE_1HOUR = 3
# 4 -   日K 线
KLINE_DAILY = 4
# 5 -   周K 线
KLINE_WEEKLY = 5
# 6 -   月K 线
KLINE_MONTHLY = 6
# 7 -   扩展市场 1 分钟
KLINE_EX_1MIN = 7
# 8 -   1 分钟K 线
KLINE_1MIN = 8
# 9 -   日K 线
KLINE_RI_K = 9
# 10 -  季K 线
KLINE_3MONTH = 10
# 11 -  年K 线
KLINE_YEARLY = 11

# 分笔行情最多2000条
MAX_TRANSACTION_COUNT = 2000

# K线数据最多800条
MAX_KLINE_COUNT = 800

FREQUENCY = ['5m', '15m', '30m', '1h', 'day', 'week', 'mon', 'ex_1m', '1m', 'dk', '3mon', 'year']

# 板块相关参数
BLOCK_SZ = 'block_zs.dat'
BLOCK_FG = 'block_fg.dat'
BLOCK_GN = 'block_gn.dat'
BLOCK_DEFAULT = 'block.dat'

TYPE_FLATS = 0
TYPE_GROUP = 1

HQ_HOSTS = [
    ('深圳双线主站1', '**************', 7709),
    ('深圳双线主站2', '***********', 7709),
    ('深圳双线主站3', '*************', 7709),
    ('深圳双线主站4', '*************', 7709),
    ('深圳双线主站5', '*************', 7709),
    ('深圳双线主站6', '**************', 7709),
    ('上海双线主站1', '*************', 7709),
    ('上海双线主站2', '*************', 7709),
    ('上海双线主站3', '***************', 7709),
    ('上海双线主站4', '************', 7709),
    ('上海双线主站5', '*************', 7709),
    ('上海双线主站6', '*************', 7709),
    ('北京双线主站1', '*************', 7709),
    ('北京双线主站2', '*************', 7709),
    ('北京双线主站3', '*************', 7709),
    ('广州双线主站1', '*************', 7709),
    ('广州双线主站2', '***********', 7709),
    ('广州双线主站3', '***************', 7709),
    ('上海双线主站7', '**************', 7709),
    ('上海双线主站8', '106.14.190.242', 7709),
    ('上海双线主站9', '121.36.225.169', 7709),
    ('上海双线主站10', '123.60.70.228', 7709),
    ('上海双线主站11', '123.60.73.44', 7709),
    ('上海双线主站12', '124.70.133.119', 7709),
    ('上海双线主站13', '124.71.187.72', 7709),
    ('上海双线主站14', '124.71.187.122', 7709),
    ('武汉电信主站1', '************9', 7709),
    ('深圳双线主站7', '47.107.64.168', 7709),
    ('北京双线主站4', '*************', 7709),
    ('广州双线主站4', '************', 7709),
    ('上海双线主站15', '************', 7709),
    ('深圳双线主站8', '*************', 7719),
    ('北京双线主站5', '**************', 7709),
    ('北京双线主站6', '*************', 7709),
    ('北京双线主站7', '*************', 7709),
    ('广州双线主站5', '***************', 7709),
    ('广州双线主站6', '***************', 7709),
    ('广州双线主站7', '***************', 7709)
]

EX_HOSTS = [
    # ('扩展市场深圳双线1', '*************', 7727),
    # ('扩展市场深圳双线2', '***********', 7727),
    # ('扩展市场深圳双线3', '*************', 7727),
    # ('扩展市场武汉主站1', '************', 7727),
    # ('扩展市场武汉主站2', '*************', 7727),
    # ('扩展市场武汉主站3', '*************', 7727),
    # ('扩展市场北京双线0', '*************', 7727),
    # ('扩展市场上海双线0', '*************', 7727),
    # ('扩展市场新加双线0', '**************', 7727),
    ('银河阿里云扩展行情', '*************', 7720),
    ('银河杭州电信扩展行情', '************', 7720),
    ('银河武汉电信扩展行情', '************', 7720),
]

GP_HOSTS = [
    ('默认财务数据线路', '*************', 7709),
]

CONFIG = {
    'SERVER': {'HQ': HQ_HOSTS, 'EX': EX_HOSTS, 'GP': GP_HOSTS},
    'BESTIP': {'HQ': '', 'EX': '', 'GP': ''},
    'TDXDIR': 'C:/new_tdx',
}


def return_last_value(retry_state):
    """return the result of the last call attempt"""
    return retry_state.outcome.result()
